# --- IQL specific parameters ---

# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000

runner: "parallel"
batch_size_run: 8
t_max: 10050000

buffer_size: 5000

# update the target network every {} episodes
target_update_interval: 200

# use the <PERSON>_Learner to train
agent_output_type: "q"
learner: "q_learner"
double_q: True
mixer: # Mixer becomes None
optimizer: "adam"

name: "iql_env=8_adam"
