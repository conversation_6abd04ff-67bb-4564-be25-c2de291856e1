# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000

runner: "parallel"
batch_size_run: 8
buffer_size: 5000
batch_size: 128

# update the target network every {} episodes
target_update_interval: 200
t_max: 10050000

# use the <PERSON>_Learner to train
agent_output_type: "q"
learner: "max_q_learner"
double_q: True
mixer: "qmix"
mixing_embed_dim: 32
hypernet_layers: 2
hypernet_embed: 64

central_loss: 1
qmix_loss: 1
w: 0.1 # $\alpha$ in the paper
hysteretic_qmix: False # False -> CW-QMIX, True -> OW-QMIX

central_mixing_embed_dim: 256
central_action_embed: 1
central_mac: "basic_central_mac"
central_agent: "central_rnn"
central_rnn_hidden_dim: 64
central_mixer: "ff"
td_lambda: 0.6
lr: 0.001

name: "cw_qmix_env=8_adam_td_lambda"