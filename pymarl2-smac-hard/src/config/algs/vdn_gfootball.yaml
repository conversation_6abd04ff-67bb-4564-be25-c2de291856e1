#--- VDN specific parameters ---
# <PERSON> Q_tot Learning

# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000

runner: "parallel"
batch_size_run: 16
buffer_size: 2000
batch_size: 128

t_max: 10050000

# update the target network every {} episodes
target_update_interval: 1

# use the Q_Learner to train
mac: "n_mac"
agent: "n_rnn" 
agent_output_type: q
rnn_hidden_dim: 256

learner: "nq_learner"
mixer: "vdn"
lr: 0.0005 # Learning rate for agents
td_lambda: 1.0
optimizer: 'adam'
gamma: 0.999

# orthogonal init for DNN
use_orthogonal: False
gain: 0.01

# rnn layer normalization
use_layer_norm: False

# Priority experience replay
use_per: False
per_alpha: 0.6
per_beta: 0.4
return_priority: True

name: "vdn_env=16_adam_td_lambda"
