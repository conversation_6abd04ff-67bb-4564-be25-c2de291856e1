# --- QMIX CONV specific parameters ---

# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000

runner: "parallel"
batch_size_run: 8
buffer_size: 5000
batch_size: 128

t_max: 10050000

# update the target network every {} episodes
target_update_interval: 200

# use the Q_Learner to train
mac: "conv_mac"
agent: "conv_agent"
agent_output_type: q
rnn_hidden_dim: 128

learner: "nq_learner"
mixer: "qmix"
mixing_embed_dim: 32
hypernet_embed: 64
lr: 0.001 # Learning rate for agents
td_lambda: 0.6
optimizer: 'adam'
frames: 4

name: "qmix_conv_env=8_adam_td_lambda"