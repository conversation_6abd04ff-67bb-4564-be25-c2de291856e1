# --- QMIX specific parameters with large networks ---
# for 3s5z_vs_3s6z

# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000 

runner: "parallel"
batch_size_run: 8 
buffer_size: 5000 
batch_size: 128
optimizer: 'adam'

t_max: 10050000

# update the target network every {} episodes
target_update_interval: 200

# use the Q_Learner to train
mac: "n_mac"
agent: "n_rnn"
agent_output_type: q
rnn_hidden_dim: 256

learner: "nq_learner"
mixer: "qmix"
mixing_embed_dim: 64
hypernet_embed: 256
lr: 0.001 # Learning rate for agents
td_lambda: 0.6
optimizer: 'adam'
weight_decay: 0

# rnn layer normalization
use_layer_norm: False

# orthogonal init for DNN
use_orthogonal: False
gain: 0.01

# Priority experience replay
use_per: False
per_alpha: 0.6
per_beta: 0.4
return_priority: False

name: "qmix_large_env=8_adam_td_lambda"
