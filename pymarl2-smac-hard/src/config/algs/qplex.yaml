# From https://github.com/wjh720/QPLEX/
# --- Qatten specific parameters ---

# use epsilon greedy action selector
action_selector: "epsilon_greedy"
epsilon_start: 1.0
epsilon_finish: 0.05
epsilon_anneal_time: 100000

runner: "parallel"
batch_size_run: 8
buffer_size: 5000
batch_size: 128

# update the target network every {} episodes
target_update_interval: 200
t_max: 10050000

# use the Q_Learner to train
mac: "n_mac"
agent: "n_rnn"
rnn_hidden_dim: 64 # Size of hidden state for default rnn agent
agent_output_type: "q"

learner: "dmaq_qatten_learner"
double_q: True
mixer: "dmaq"
mixing_embed_dim: 32
hypernet_embed: 64
adv_hypernet_layers: 2
adv_hypernet_embed: 64
td_lambda: 0.6
lr: 0.001

num_kernel: 4
is_minus_one: True
weighted_head: True
is_adv_attention: True
is_stop_gradient: True

name: "qplex_env=8_adam_td_lambda"
