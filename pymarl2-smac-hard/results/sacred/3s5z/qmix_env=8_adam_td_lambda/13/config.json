{"action_selector": "epsilon_greedy", "agent": "n_rnn", "agent_output_type": "q", "batch_size": 128, "batch_size_run": 6, "buffer_cpu_only": true, "buffer_size": 5000, "checkpoint_path": "", "critic_lr": 0.0005, "env": "sc2", "env_args": {"continuing_episode": false, "debug": false, "difficulty": "7", "game_version": null, "heuristic_ai": false, "heuristic_rest": false, "map_name": "3s5z", "move_amount": 2, "obs_all_health": true, "obs_instead_of_state": false, "obs_last_action": false, "obs_own_health": true, "obs_pathing_grid": false, "obs_terrain_height": false, "obs_timestep_number": false, "replay_dir": "", "replay_prefix": "", "reward_death_value": 10, "reward_defeat": 0, "reward_negative_scale": 0.5, "reward_only_positive": true, "reward_scale": true, "reward_scale_rate": 20, "reward_sparse": false, "reward_win": 200, "seed": null, "state_last_action": true, "state_timestep_number": false, "step_mul": 8, "window_size_x": 640, "window_size_y": 480}, "epsilon_anneal_time": 100000, "epsilon_finish": 0.05, "epsilon_start": 1.0, "evaluate": false, "gain": 0.01, "gamma": 0.99, "grad_norm_clip": 10, "hypernet_embed": 64, "label": "default_label", "learner": "nq_learner", "learner_log_interval": 80000, "load_step": 0, "local_results_path": "results", "log_interval": 500, "lr": 0.001, "mac": "n_mac", "mixer": "qmix", "mixing_embed_dim": 32, "name": "qmix_env=8_adam_td_lambda", "obs_agent_id": true, "obs_last_action": true, "optim_alpha": 0.99, "optim_eps": 1e-05, "optimizer": "adam", "per_alpha": 0.6, "per_beta": 0.4, "q_lambda": true, "repeat_id": 1, "return_priority": false, "rnn_hidden_dim": 64, "run": "default", "runner": "parallel", "runner_log_interval": 80000, "save_model": true, "save_model_interval": 50000, "save_replay": false, "seed": 1, "t_max": 10050000, "target_update_interval": 200, "td_lambda": 0.6, "test_greedy": true, "test_interval": 80000, "test_nepisode": 32, "use_cuda": true, "use_layer_norm": false, "use_orthogonal": false, "use_per": false, "use_tensorboard": true, "use_wandb": false}