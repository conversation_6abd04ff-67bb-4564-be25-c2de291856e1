[INFO 13:36:04] pymarl Running command 'my_main'
[INFO 13:36:04] pymarl Started run with ID "11"
[DEBUG 13:36:04] pymarl Starting Heartbeat
[DEBUG 13:36:04] my_main Started
[WARNING 13:36:04] my_main CUDA flag use_cuda was switched OFF automatically because no CUDA devices are available!
[INFO 13:36:04] my_main Experiment Parameters:
[INFO 13:36:04] my_main 

{   'action_selector': 'epsilon_greedy',
    'agent': 'n_rnn',
    'agent_output_type': 'q',
    'batch_size': 128,
    'batch_size_run': 6,
    'buffer_cpu_only': True,
    'buffer_size': 5000,
    'checkpoint_path': '',
    'critic_lr': 0.0005,
    'env': 'sc2',
    'env_args': {   'continuing_episode': False,
                    'debug': False,
                    'difficulty': '7',
                    'game_version': None,
                    'heuristic_ai': False,
                    'heuristic_rest': False,
                    'map_name': '3s5z',
                    'move_amount': 2,
                    'obs_all_health': True,
                    'obs_instead_of_state': False,
                    'obs_last_action': False,
                    'obs_own_health': True,
                    'obs_pathing_grid': False,
                    'obs_terrain_height': False,
                    'obs_timestep_number': False,
                    'replay_dir': '',
                    'replay_prefix': '',
                    'reward_death_value': 10,
                    'reward_defeat': 0,
                    'reward_negative_scale': 0.5,
                    'reward_only_positive': True,
                    'reward_scale': True,
                    'reward_scale_rate': 20,
                    'reward_sparse': False,
                    'reward_win': 200,
                    'seed': 1,
                    'state_last_action': True,
                    'state_timestep_number': False,
                    'step_mul': 8,
                    'window_size_x': 640,
                    'window_size_y': 480},
    'epsilon_anneal_time': 100000,
    'epsilon_finish': 0.05,
    'epsilon_start': 1.0,
    'evaluate': False,
    'gain': 0.01,
    'gamma': 0.99,
    'grad_norm_clip': 10,
    'hypernet_embed': 64,
    'label': 'default_label',
    'learner': 'nq_learner',
    'learner_log_interval': 80000,
    'load_step': 0,
    'local_results_path': 'results',
    'log_interval': 80000,
    'lr': 0.001,
    'mac': 'n_mac',
    'mixer': 'qmix',
    'mixing_embed_dim': 32,
    'name': 'qmix_env=8_adam_td_lambda',
    'obs_agent_id': True,
    'obs_last_action': True,
    'optim_alpha': 0.99,
    'optim_eps': 1e-05,
    'optimizer': 'adam',
    'per_alpha': 0.6,
    'per_beta': 0.4,
    'q_lambda': True,
    'repeat_id': 1,
    'return_priority': False,
    'rnn_hidden_dim': 64,
    'run': 'default',
    'runner': 'parallel',
    'runner_log_interval': 80000,
    'save_model': False,
    'save_model_interval': 2000000,
    'save_replay': False,
    'seed': 1,
    't_max': 10050000,
    'target_update_interval': 200,
    'td_lambda': 0.6,
    'test_greedy': True,
    'test_interval': 80000,
    'test_nepisode': 30,
    'use_cuda': False,
    'use_layer_norm': False,
    'use_orthogonal': False,
    'use_per': False,
    'use_tensorboard': True,
    'use_wandb': False}

[DEBUG 13:36:06] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 13:36:06] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 13:36:07] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 13:36:07] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 13:36:07] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
Mixer Size: 
60.417K
[DEBUG 13:36:07] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[INFO 13:36:09] my_main Beginning training for 10050000 timesteps
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57316 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-9b5rwm88/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57320 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-pe46r23m/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57321 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-wn_qfupz/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57322 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-vz2422q5/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57331 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-uraxd3k7/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57332 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-zinxhers/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 0, running: True
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 0, running: True
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 0, running: True
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 0, running: True
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 0, running: True
[INFO 13:36:09] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 0, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 1, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 1, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 1, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 1, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 1, running: True
[INFO 13:36:10] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 1, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 2, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 2, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 2, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 2, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 2, running: True
[INFO 13:36:11] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 2, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 3, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 3, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 3, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 3, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 3, running: True
[INFO 13:36:12] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 3, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 4, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 4, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 4, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 4, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 4, running: True
[INFO 13:36:13] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 4, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 5, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 5, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 5, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 5, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 5, running: True
[INFO 13:36:14] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 5, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 6, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 6, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 6, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 6, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 6, running: True
[INFO 13:36:15] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 6, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 7, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 7, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 7, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 7, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 7, running: True
[INFO 13:36:16] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 7, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 8, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 8, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 8, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 8, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 8, running: True
[INFO 13:36:17] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 8, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 9, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 9, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 9, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 9, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 9, running: True
[INFO 13:36:18] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 9, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 10, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 10, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 10, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 10, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 10, running: True
[INFO 13:36:19] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 10, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 11, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 11, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 11, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 11, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 11, running: True
[INFO 13:36:20] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 11, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 12, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 12, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 12, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 12, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 12, running: True
[INFO 13:36:21] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 12, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 13, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 13, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 13, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 13, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 13, running: True
[INFO 13:36:22] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 13, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 14, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 14, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 14, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 14, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 14, running: True
[INFO 13:36:23] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 14, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 15, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 15, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 15, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 15, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 15, running: True
[INFO 13:36:24] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 15, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 16, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 16, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 16, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 16, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 16, running: True
[INFO 13:36:25] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 16, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 17, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 17, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 17, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 17, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 17, running: True
[INFO 13:36:26] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 17, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 18, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 18, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 18, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 18, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 18, running: True
[INFO 13:36:27] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 18, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57321/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57331/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57532 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-tqkjjqed/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57316/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 0, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57322/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57332/sc2api, attempt: 19, running: True
[INFO 13:36:28] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57537 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-oiv832j_/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 0, running: True
[INFO 13:36:28] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57539 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-u46n18k6/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:28] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57540 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-2h1556xb/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 0, running: True
[INFO 13:36:28] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57541 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-f2ezafib/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:28] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 0, running: True
[INFO 13:36:29] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 0, running: True
[INFO 13:36:29] absl Connecting to: ws://127.0.0.1:57320/sc2api, attempt: 20, running: True
[INFO 13:36:29] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 1, running: True
[INFO 13:36:29] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 1, running: True
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 1, running: True
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 1, running: True
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 1, running: True
[INFO 13:36:30] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 57555 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-i72wdy9d/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 0, running: True
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 2, running: True
[INFO 13:36:30] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 2, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 2, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 2, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 2, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 1, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 3, running: True
[INFO 13:36:31] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 3, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 3, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 3, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 3, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 2, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 4, running: True
[INFO 13:36:32] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 4, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 4, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 4, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 4, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 3, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 5, running: True
[INFO 13:36:33] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 5, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 5, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 5, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 5, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 4, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 6, running: True
[INFO 13:36:34] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 6, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 6, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 6, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 6, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 5, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 7, running: True
[INFO 13:36:35] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 7, running: True
[INFO 13:36:36] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 7, running: True
[INFO 13:36:36] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 7, running: True
[INFO 13:36:36] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 7, running: True
[INFO 13:36:36] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 6, running: True
[INFO 13:36:36] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 8, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 8, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 8, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 8, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 8, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 7, running: True
[INFO 13:36:37] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 9, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 9, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 9, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 9, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 9, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 8, running: True
[INFO 13:36:38] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 10, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 10, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 10, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 10, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 10, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 9, running: True
[INFO 13:36:39] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 11, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 11, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 11, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 11, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 11, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 10, running: True
[INFO 13:36:40] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 12, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 12, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 12, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 12, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 12, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 11, running: True
[INFO 13:36:41] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 13, running: True
[INFO 13:36:42] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 13, running: True
[INFO 13:36:42] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 13, running: True
[INFO 13:36:42] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 13, running: True
[INFO 13:36:42] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 13, running: True
[INFO 13:36:42] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 12, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 14, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 14, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 14, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 14, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 14, running: True
[INFO 13:36:43] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 13, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 15, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 15, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 15, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 15, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 15, running: True
[INFO 13:36:44] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 14, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 16, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 16, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 16, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 16, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 16, running: True
[INFO 13:36:45] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 15, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 17, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 17, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 17, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 17, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 17, running: True
[INFO 13:36:46] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 16, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 18, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 18, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 18, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 18, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 18, running: True
[INFO 13:36:47] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 17, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 19, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 19, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 19, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 19, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 19, running: True
[INFO 13:36:48] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 18, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 20, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 20, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 20, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 20, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 20, running: True
[INFO 13:36:49] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 19, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 21, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 21, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 21, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 21, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 21, running: True
[INFO 13:36:50] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 20, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 22, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 22, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 22, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 22, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 22, running: True
[INFO 13:36:51] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 21, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57532/sc2api, attempt: 23, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57537/sc2api, attempt: 23, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57539/sc2api, attempt: 23, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57540/sc2api, attempt: 23, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57541/sc2api, attempt: 23, running: True
[INFO 13:36:52] absl Connecting to: ws://127.0.0.1:57555/sc2api, attempt: 22, running: True
2025-07-17 13:40:07.154 SC2[36667:520112] error messaging the mach port for IMKCFRunLoopWakeUpReliable
/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/components/episode_buffer.py:103: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1729647065806/work/torch/csrc/utils/tensor_new.cpp:281.)
  v = th.tensor(v, dtype=dtype, device=self.device)
/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/components/episode_buffer.py:103: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).
  v = th.tensor(v, dtype=dtype, device=self.device)
[INFO 13:40:32] my_main t_env: 326 / 10050000
[INFO 13:40:32] my_main Estimated time left: 9 hours, 7 minutes, 2 seconds. Time passed: 4 minutes, 22 seconds
[INFO 13:48:10] my_main Updated target network
[INFO 13:54:38] my_main Updated target network
[INFO 14:00:57] my_main Updated target network
[INFO 14:07:21] my_main Updated target network
[INFO 14:13:54] my_main Updated target network
[INFO 14:20:41] my_main Updated target network
[INFO 14:27:32] my_main Updated target network
[INFO 14:32:18] my_main Recent Stats | t_env:      80061 | Episode:     1584
battle_won_mean:           0.0000	dead_allies_mean:          8.0000	dead_enemies_mean:         0.0000	ep_length_mean:           54.3333
epsilon:                   1.0000	grad_norm:                 4.7106	loss_td:                   0.5817	q_taken_mean:              0.0045
return_mean:               3.5339	return_std:                0.5418	target_mean:               0.1168	td_error_abs:              0.5817
test_battle_won_mean:      0.0000	test_dead_allies_mean:     8.0000	test_dead_enemies_mean:    1.3667	test_ep_length_mean:      65.4667
test_return_mean:          5.0531	test_return_std:           1.3751	
[INFO 14:32:30] my_main t_env: 80373 / 10050000
[INFO 14:32:31] my_main Estimated time left: 4 days, 11 hours, 54 minutes, 18 seconds. Time passed: 56 minutes, 21 seconds
[INFO 14:35:00] my_main Updated target network
[INFO 14:41:35] my_main Updated target network
[INFO 14:49:36] my_main Updated target network
[INFO 14:56:32] my_main Updated target network
[INFO 15:04:50] my_main Updated target network
[INFO 15:12:56] my_main Updated target network
[INFO 15:21:47] my_main Updated target network
Process Process-3:
Process Process-1:
Process Process-6:
Process Process-5:
Process Process-4:
Process Process-2:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 229, in env_worker
    cmd, data = remote.recv()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/connection.py", line 250, in recv
    buf = self._recv_bytes()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/connection.py", line 414, in _recv_bytes
    buf = self._recv(4)
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/connection.py", line 379, in _recv
    chunk = read(handle, remaining)
KeyboardInterrupt
Traceback (most recent call last):
Traceback (most recent call last):
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 233, in env_worker
    reward, terminated, env_info = env.step(actions)
Traceback (most recent call last):
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/starcraft2.py", line 589, in step
    obs = self.parallel.run(c.observe for c in self._controllers)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/run_parallel.py", line 64, in run
    done, not_done = futures.wait(futs, self._timeout, futures.FIRST_EXCEPTION)
  File "/opt/anaconda3/envs/smac/lib/python3.10/concurrent/futures/_base.py", line 307, in wait
    waiter.event.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
Traceback (most recent call last):
KeyboardInterrupt
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
Traceback (most recent call last):
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 233, in env_worker
    reward, terminated, env_info = env.step(actions)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/starcraft2.py", line 589, in step
    obs = self.parallel.run(c.observe for c in self._controllers)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/run_parallel.py", line 64, in run
    done, not_done = futures.wait(futs, self._timeout, futures.FIRST_EXCEPTION)
  File "/opt/anaconda3/envs/smac/lib/python3.10/concurrent/futures/_base.py", line 307, in wait
    waiter.event.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 233, in env_worker
    reward, terminated, env_info = env.step(actions)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/starcraft2.py", line 589, in step
    obs = self.parallel.run(c.observe for c in self._controllers)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/run_parallel.py", line 64, in run
    done, not_done = futures.wait(futs, self._timeout, futures.FIRST_EXCEPTION)
  File "/opt/anaconda3/envs/smac/lib/python3.10/concurrent/futures/_base.py", line 307, in wait
    waiter.event.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
KeyboardInterrupt
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 233, in env_worker
    reward, terminated, env_info = env.step(actions)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/starcraft2.py", line 585, in step
    self.parallel.run((c.step, self._step_mul) for c in self._controllers)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/run_parallel.py", line 64, in run
    done, not_done = futures.wait(futs, self._timeout, futures.FIRST_EXCEPTION)
  File "/opt/anaconda3/envs/smac/lib/python3.10/concurrent/futures/_base.py", line 307, in wait
    waiter.event.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/smac/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/runners/parallel_runner.py", line 233, in env_worker
    reward, terminated, env_info = env.step(actions)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/starcraft2.py", line 589, in step
    obs = self.parallel.run(c.observe for c in self._controllers)
  File "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/envs/smac_hard/env/starcraft2/run_parallel.py", line 64, in run
    done, not_done = futures.wait(futs, self._timeout, futures.FIRST_EXCEPTION)
  File "/opt/anaconda3/envs/smac/lib/python3.10/concurrent/futures/_base.py", line 307, in wait
    waiter.event.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/anaconda3/envs/smac/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
