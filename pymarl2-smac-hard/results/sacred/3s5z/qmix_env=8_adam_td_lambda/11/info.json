{"battle_won_mean": [0.0, 0.0], "battle_won_mean_T": [326, 80373], "dead_allies_mean": [8.0, 8.0], "dead_allies_mean_T": [326, 80373], "dead_enemies_mean": [0.0, 0.4185606060606061], "dead_enemies_mean_T": [326, 80373], "ep_length_mean": [54.333333333333336, 50.53472222222222], "ep_length_mean_T": [326, 80373], "episode": [1584], "episode_T": [80061], "epsilon": [1.0, 0.23942050000000015], "epsilon_T": [326, 80373], "grad_norm": [{"py/reduce": [{"py/function": "torch._utils._rebuild_tensor_v2"}, {"py/tuple": [{"py/reduce": [{"py/function": "torch.storage._load_from_bytes"}, {"py/tuple": [{"py/b64": "gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAAAGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAAaW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3JhZ2UKcQFYCgAAADU3Nzk4NDU3NDRxAlgDAAAAY3B1cQNLAU50cQRRLoACXXEAWAoAAAA1Nzc5ODQ1NzQ0cQFhLgEAAAAAAAAA8byWQA=="}]}]}, 0, {"py/tuple": []}, {"py/tuple": []}, false, {"py/reduce": [{"py/type": "collections.OrderedDict"}, {"py/tuple": []}, null, null, {"py/tuple": []}]}]}]}, {"py/reduce": [{"py/function": "torch._utils._rebuild_tensor_v2"}, {"py/tuple": [{"py/reduce": [{"py/function": "torch.storage._load_from_bytes"}, {"py/tuple": [{"py/b64": "gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAAAGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAAaW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3JhZ2UKcQFYCgAAADU0OTkzNTk1NjhxAlgDAAAAY3B1cQNLAU50cQRRLoACXXEAWAoAAAA1NDk5MzU5NTY4cQFhLgEAAAAAAAAAWXcfPw=="}]}]}, 0, {"py/tuple": []}, {"py/tuple": []}, false, {"py/reduce": [{"py/type": "collections.OrderedDict"}, {"py/tuple": []}, null, null, {"py/tuple": []}]}]}]}], "grad_norm_T": [6610, 86920], "loss_td": [0.5817267298698425, 0.028123894706368446], "loss_td_T": [6610, 86920], "q_taken_mean": [0.004510445744287315, 0.2408232868786719], "q_taken_mean_T": [6610, 86920], "return_mean": [{"dtype": "float64", "py/object": "numpy.float64", "value": 3.5339403973509937}, {"dtype": "float64", "py/object": "numpy.float64", "value": 4.953550404709345}], "return_mean_T": [326, 80373], "return_std": [{"dtype": "float64", "py/object": "numpy.float64", "value": 0.5418173363582905}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.2507028640989204}], "return_std_T": [326, 80373], "target_mean": [0.11676334802975355, 0.2560618629998164], "target_mean_T": [6610, 86920], "td_error_abs": [0.5817267120842108, 0.028123894516302615], "td_error_abs_T": [6610, 86920], "test_battle_won_mean": [0.0, 0.0], "test_battle_won_mean_T": [326, 80373], "test_dead_allies_mean": [8.0, 8.0], "test_dead_allies_mean_T": [326, 80373], "test_dead_enemies_mean": [1.3666666666666667, 1.8666666666666667], "test_dead_enemies_mean_T": [326, 80373], "test_ep_length_mean": [65.46666666666667, 53.86666666666667], "test_ep_length_mean_T": [326, 80373], "test_return_mean": [{"dtype": "float64", "py/object": "numpy.float64", "value": 5.053145695364238}, {"dtype": "float64", "py/object": "numpy.float64", "value": 7.094426048565121}], "test_return_mean_T": [326, 80373], "test_return_std": [{"dtype": "float64", "py/object": "numpy.float64", "value": 1.3751262911595281}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.4163561339098032}], "test_return_std_T": [326, 80373]}