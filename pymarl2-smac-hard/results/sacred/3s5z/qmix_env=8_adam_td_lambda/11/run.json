{"artifacts": [], "command": "my_main", "experiment": {"base_dir": "/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src", "dependencies": ["numpy==2.0.1", "PyYAML==6.0.2", "sacred==0.8.7", "torch==2.5.1"], "mainfile": "main.py", "name": "pymarl", "repositories": [], "sources": [["main.py", "_sources/main_c81c42f92f361e97dc35e2cb9570b67c.py"], ["utils/logging.py", "_sources/logging_52ba9f7fb556532bcacad6e20491bc1f.py"]]}, "heartbeat": "2025-07-17T07:25:48.350364", "host": {"ENV": {}, "cpu": "Apple M3", "hostname": "**************", "os": ["<PERSON>", "macOS-15.5-arm64-arm-64bit"], "python_version": "3.10.13"}, "meta": {"command": "my_main", "config_updates": {"batch_size_run": 6, "env_args": {"map_name": "3s5z"}, "seed": 1}, "named_configs": [], "options": {"--beat-interval": null, "--capture": null, "--comment": null, "--debug": false, "--enforce_clean": false, "--file_storage": null, "--force": false, "--help": false, "--id": null, "--loglevel": null, "--mongo_db": null, "--name": null, "--pdb": false, "--print-config": false, "--priority": null, "--queue": false, "--s3": null, "--sql": null, "--tiny_db": null, "--unobserved": false, "COMMAND": null, "UPDATE": ["env_args.map_name=3s5z", "seed=1", "batch_size_run=6"], "help": false, "with": true}}, "resources": [], "result": null, "start_time": "2025-07-17T05:36:04.420055", "status": "INTERRUPTED", "stop_time": "2025-07-17T07:25:48.363236"}