from smac_hard.env import StarCraft2Env
from submission.team1.red import PolicyRed as team1_red
from submission.team1.blue import PolicyBlue as team1_blue
from submission.team2.red import PolicyRed as team2_red
from submission.team2.blue import PolicyBlue as team2_blue




import numpy as np


def main():

    env = StarCraft2Env(map_name="3s5z", mode='multi')

    n_episodes = 5

    for e in range(n_episodes):

        r1 = team1_red()
        b2 = team2_blue()

        env.reset()
        terminated = False
        red_episode_reward = 0
        blue_episode_reward = 0


        while not terminated:


            red_actions = r1.policy(env)

            blue_actions = b2.policy(env)

            rewards, terminateds, _ = env.step([red_actions, blue_actions])
            red_episode_reward += rewards[0]
            blue_episode_reward += rewards[1]
            terminated = terminateds[0]


        print("Total reward in episode {} = {} and {}".format(e, red_episode_reward, blue_episode_reward))

    env.close()

if __name__ == '__main__':
    main()