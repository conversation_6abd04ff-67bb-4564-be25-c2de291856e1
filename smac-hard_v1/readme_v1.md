# SMAC-HARD-MULTI

### 示例代码：

~~~python
from smac_hard.env import StarCraft2Env
from submission.team1.red import PolicyRed as team1_red
from submission.team1.blue import PolicyBlue as team1_blue
from submission.team2.red import PolicyRed as team2_red
from submission.team2.blue import PolicyBlue as team2_blue




import numpy as np


def main():

    env = StarCraft2Env(map_name="3s5z", mode='multi')

    n_episodes = 5

    for e in range(n_episodes):

        r1 = team1_red()
        b2 = team2_blue()

        env.reset()
        terminated = False
        red_episode_reward = 0
        blue_episode_reward = 0


        while not terminated:


            red_actions = r1.policy(env)

            blue_actions = b2.policy(env)

            rewards, terminateds, _ = env.step([red_actions, blue_actions])
            red_episode_reward += rewards[0]
            blue_episode_reward += rewards[1]
            terminated = terminateds[0]


        print("Total reward in episode {} = {} and {}".format(e, red_episode_reward, blue_episode_reward))

    env.close()

if __name__ == '__main__':
    main()
~~~

### 环境说明

* 通过StarCraft2Env类创建SMAC任务，并以env保存下来；
* 环境为3s5z:每方控制3个Stalker角色+5个Zealot角色；
* env包含环境中的信息，包括观测、行为空间等，同时可以通过env获取此刻每个智能的观测、可用行为、全局状态等；
* 训练过程支持CTDE，即在训练的时候可以使用全局状态，但是在测试的过程中（作业评比的过程中），智能体只可以使用部分观测，不可以使用全局状态，在作业评分时候会看代码实现过程。
* 最野将会以**组队**的形式提交，分组方式可以按大作业组队的情况进行。
* 实际推演中将建立submissions文件夹，将各位队伍的提交结果放入其中。具体调用的过程中将会通过submission.team1引入red/blue文件中的PolicyRed或者PolicyBlue，
~~~
from smac_hard.env import StarCraft2Env
from submission.team1.red import PolicyRed as team1_red
from submission.team1.blue import PolicyBlue as team1_blue
from submission.team2.red import PolicyRed as team2_red
from submission.team2.blue import PolicyBlue as team2_blue
~~~
* 然后会在主函数中实例化PolicyRed，并将env传入调用policy方法
~~~
r1 = team1_red()
b2 = team2_blue()
...
    red_actions = r1.policy(env)
    blue_actions = b2.policy(env)
~~~
* 在具体环境实现中采用了parallel包，所以step函数中红蓝方的action顺序不影响rollout结果，rollout结果由星际争霸环境给出。
* 训练过程可以用任意的算法、代码库等。提交时需要满足下述提交说明（提交前尽量先使用己方的红蓝双方策略完成测试代码）。
  

### 提交说明

~~~
...
|submissions
|  |team1
|  |  |red.py
|  |  |blue.py
|  |  |other resources(models, feature engineering)
|  |team2
|  |  |red.py
|  |  |blue.py
|  |  |other resources(models, feature engineering)
...
~~~

* 由于推演需要红蓝双方，因此在自己训练的时候也需要提供双方文件。初期可以采用随机策略、single-mode对手策略、或者自博弈的方式训练。后面如果队伍间分别训练出模型也可以互相使用对方的作为对手。

**环境也正处于测试中，如果有环境方面的问题可以联系我**

**最后祝大家取得好成绩😁**

