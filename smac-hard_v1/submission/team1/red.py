import torch as th
import numpy as np
import os
from types import SimpleNamespace as SN
import yaml

from .modules.agents import REGISTR<PERSON> as agent_REGISTRY
from .modules.mixers import R<PERSON><PERSON><PERSON><PERSON> as mixer_REGISTRY

class PolicyRed:
    def __init__(self):
        # Load model config
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "config", "model_config.yaml")
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        self.args = SN(**config)
        self.args.use_cuda = th.cuda.is_available()
        self.args.device = "cuda" if self.args.use_cuda else "cpu"

        # Compute agent_input_shape
        agent_input_shape = self.args.obs_shape
        if getattr(self.args, "obs_last_action", False):
            agent_input_shape += self.args.n_actions
        if getattr(self.args, "obs_agent_id", False):
            agent_input_shape += self.args.n_agents

        # Ensure agent_input_shape matches expected (150)
        assert agent_input_shape == 150, f"agent_input_shape should be 150, got {agent_input_shape}"

        self.agent = agent_REGISTRY["n_rnn"](agent_input_shape, self.args)
        self.mixer = mixer_REGISTRY["qmix"](self.args)

        model_path = os.path.join(current_dir, "models")
        try:
            self.agent.load_state_dict(th.load(os.path.join(model_path, "agent.th"), map_location=self.args.device))
            self.mixer.load_state_dict(th.load(os.path.join(model_path, "mixer.th"), map_location=self.args.device))
            print(f"PolicyRed: Models loaded successfully from {model_path}")
        except FileNotFoundError:
            print(f"PolicyRed: Model files not found in {model_path}. Ensure agent.th and mixer.th are present.")
            pass

        self.agent.eval()
        self.mixer.eval()
        if self.args.use_cuda:
            self.agent.to(self.args.device)
            self.mixer.to(self.args.device)
        self.hidden_state = None
        self.last_action_onehot = None

    def policy(self, env):
        if env._episode_steps == 0:
            self.hidden_state = self.agent.init_hidden().unsqueeze(0).expand(1, self.args.n_agents, -1)
            self.last_action_onehot = th.zeros(1, self.args.n_agents, self.args.n_actions, device=self.args.device)

        obs_all_agents_np = env.get_obs()
        avail_actions_all_agents_np = env.get_avail_actions()
        obs_tensor = th.tensor(np.array(obs_all_agents_np), dtype=th.float32, device=self.args.device).unsqueeze(0)
        avail_actions_tensor = th.tensor(np.array(avail_actions_all_agents_np), dtype=th.float32, device=self.args.device).unsqueeze(0)

        inputs = [obs_tensor]
        if self.args.obs_last_action:
            inputs.append(self.last_action_onehot)
        if self.args.obs_agent_id:
            agent_ids = th.eye(self.args.n_agents, device=self.args.device).unsqueeze(0).expand(1, -1, -1)
            inputs.append(agent_ids)
        agent_inputs = th.cat([x.reshape(1, self.args.n_agents, -1) for x in inputs], dim=-1)

        agent_outs, self.hidden_state = self.agent(agent_inputs, self.hidden_state)
        masked_q_values = agent_outs.clone()
        masked_q_values[avail_actions_tensor == 0] = -float("inf")
        chosen_actions_tensor = masked_q_values.max(dim=2)[1]

        for agent_id in range(self.args.n_agents):
            # 实时获取该agent的可用动作，确保与环境同步
            avail = np.array(env.get_avail_agent_actions(agent_id))
            action = int(chosen_actions_tensor[0, agent_id].item())
            print(f"[DEBUG] Agent {agent_id} chosen action: {action}, avail: {avail}")
            if avail[action] != 1:
                available_actions = np.where(avail == 1)[0]
                if len(available_actions) > 0:
                    chosen_actions_tensor[0, agent_id] = int(available_actions[0])
                    print(f"[FIX] Agent {agent_id} fallback to {available_actions[0]}")
                else:
                    chosen_actions_tensor[0, agent_id] = 0
                    print(f"[FIX] Agent {agent_id} fallback to no-op (0)")
            # 最终断言
            assert avail[int(chosen_actions_tensor[0, agent_id].item())] == 1, \
                f"Agent {agent_id} still has invalid action {chosen_actions_tensor[0, agent_id].item()}, avail={avail}"
        chosen_actions_list = [int(a) for a in chosen_actions_tensor.squeeze(0).tolist()]
        return chosen_actions_list
