# QMIX 算法配置 (用于提交的策略)
# 注意: 这些参数需要与你训练模型时使用的参数保持一致
# 否则可能导致模型加载或推理错误

# 智能体和环境相关参数 (示例值，请根据实际情况调整)
n_agents: 8 # 示例值，例如 3s5z 地图有 8 个智能体
n_actions: 6 # 示例值，例如 SMAC 环境的动作空间大小
state_shape: 200 # 示例值，请根据 env.get_env_info()["state_shape"] 调整
obs_shape: 80 # 示例值，请根据 env.get_env_info()["obs_shape"] 调整
episode_limit: 150 # 示例值，请根据地图的 episode_limit 调整

# Agent 网络参数
rnn_hidden_dim: 64 # 示例值，与 n_rnn_agent.py 中的定义一致

# Mixer 网络参数
mixing_embed_dim: 32
hypernet_embed: 64
hypernet_layers: 2 # 示例值，与 qmix.py 中的定义一致
abs: True # 示例值，与 qmix.py 中的定义一致

# Q-learning 相关参数
gamma: 0.99 # 示例值
td_lambda: 0.6 # TD(lambda) 参数
q_lambda: True # 启用 Q(lambda) (ATD) 更新

# 其他可能需要的参数 (根据你的实际模型和代码需求添加)
# 例如，如果你的 Agent 或 Mixer 依赖于其他 args 参数，请在此处添加
