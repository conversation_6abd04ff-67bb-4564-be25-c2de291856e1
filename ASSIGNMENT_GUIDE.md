# SMAC-HARD-MULTI 作业指导

## 环境配置与依赖安装

在开始训练和运行策略之前，请确保你的 Conda 环境已安装以下必要的 Python 包：

```bash
# 核心依赖
conda install pytorch torchvision torchaudio cpuonly -c pytorch 
# 如果使用GPU，请将cpuonly替换为cudatoolkit=版本号
conda install numpy pyyaml matplotlib scipy

# StarCraft II 环境依赖
pip install pysc2

# 实验管理与日志
pip install sacred
pip install tensorboard_logger # 或 pip install tensorboard
pip install wandb # 可选，用于更丰富的实验跟踪
```

**注意：**
*   请根据你的 PyTorch 版本和 CUDA 需求调整 `conda install pytorch` 命令。
*   安装 `pysc2` 后，你还需要手动下载 StarCraft II 游戏客户端，并将其解压到 PySC2 能够找到的路径（通常是 `~/StarCraftII` 或通过环境变量 `SC2PATH` 指定）。
*   确保你的 StarCraft II 游戏版本与 `pymarl2-smac-hard` 兼容。

---

本文件旨在为“多智能体短学期”课程的 SMAC-HARD-MULTI 作业提供详细指导，包括如何利用 `pymarl2-smac-hard` 中的现有算法，以及在 `smac-hard_v1` 框架下完成作业的具体要求。

## 1. 项目概览

`SMAC-HARD-MULTI` 项目是一个基于 StarCraft II (星际争霸 II) 的多智能体微观管理环境，旨在评估和开发多智能体强化学习算法。作业要求以团队形式提交红蓝双方的智能体策略，并在指定地图上进行对抗。

## 2. 环境说明

环境通过 `smac_hard.env.StarCraft2Env` 类进行创建和交互。

**核心特点：**
*   **地图**: 示例中使用的地图是 `3s5z`，表示每方控制 3 个 Stalker (追猎者) 和 5 个 Zealot (狂热者) 角色。实际作业可能会使用其他地图，请留意通知。
*   **环境信息**: `env` 对象包含环境的各种信息，如观测 (observation)、行为空间 (action space) 等。你可以通过 `env.get_obs()` 获取当前每个智能体的局部观测，`env.get_avail_actions()` 获取可用行为，以及 `env.get_state()` 获取全局状态。
*   **CTDE (Centralized Training, Decentralized Execution)**:
    *   **训练阶段**: 允许使用全局状态 (`env.get_state()`) 进行训练，这对于一些需要全局信息的算法（如 QMIX, COMA 等）非常有用。
    *   **测试/评比阶段**: **智能体只允许使用局部观测 (`env.get_obs_agent(agent_id)`)，严禁使用全局状态。** 评分时会检查代码实现，确保智能体在决策时仅依赖其局部观测。
*   **行动顺序**: `env.step([red_actions, blue_actions])` 函数中红蓝双方的行动顺序不影响最终的 Rollout 结果，结果由星际争霸环境决定。

**示例环境交互代码 (`smac-hard_v1/test.py` 类似结构):**

```python
from smac_hard.env import StarCraft2Env
from submission.team1.red import PolicyRed as team1_red
from submission.team2.blue import PolicyBlue as team2_blue

import numpy as np

def main():
    env = StarCraft2Env(map_name="3s5z", mode='multi')
    n_episodes = 5

    for e in range(n_episodes):
        r1 = team1_red()
        b2 = team2_blue()

        env.reset()
        terminated = False
        red_episode_reward = 0
        blue_episode_reward = 0

        while not terminated:
            red_actions = r1.policy(env) # 在这里，r1.policy 只能使用 env.get_obs()
            blue_actions = b2.policy(env) # 在这里，b2.policy 只能使用 env.get_obs()

            rewards, terminateds, _ = env.step([red_actions, blue_actions])
            red_episode_reward += rewards[0]
            blue_episode_reward += rewards[1]
            terminated = terminateds[0]

        print(f"Total reward in episode {e} = {red_episode_reward} and {blue_episode_reward}")

    env.close()

if __name__ == '__main__':
    main()
```

## 3. 作业提交细节

你的提交需要遵循以下文件结构，并放置在项目根目录下的 `submission` 文件夹中：

```
submissions/
├── team1/
│   ├── red.py
│   ├── blue.py
│   └── other resources (models, feature engineering, etc.)
└── team2/
    ├── red.py
    ├── blue.py
    └── other resources (models, feature engineering, etc.)
```

**提交要求：**
*   **红蓝双方策略**: 每个队伍需要提供 `red.py` 和 `blue.py` 文件，分别实现红方和蓝方的智能体策略。
*   **策略类**: 在 `red.py` 和 `blue.py` 中，你需要实现 `PolicyRed` 和 `PolicyBlue` 类。这些类将会在主函数中被实例化，并通过调用其 `policy(env)` 方法来获取智能体的行动。
*   **`policy(env)` 方法**: 这是你实现智能体决策逻辑的核心方法。**请务必注意，在此方法中，你只能通过 `env.get_obs()` 或 `env.get_obs_agent(agent_id)` 获取局部观测信息来做出决策。严禁访问 `env.get_state()`。**
*   **其他资源**: 如果你的策略需要加载预训练模型、进行特征工程或其他辅助文件，请将它们放置在对应的 `teamX` 文件夹内。

## 4. 算法参考 (`pymarl2-smac-hard`)

`pymarl2-smac-hard` 仓库提供了多种先进的多智能体强化学习算法实现，你可以参考并将其应用于你的作业中。

**主要算法目录：**
*   `pymarl2-smac-hard/src/config/algs/`: 包含各种算法的配置文件（例如 `qmix.yaml`, `coma.yaml`, `ippo.yaml`, `vdn.yaml` 等）。这些文件定义了算法的超参数、网络结构等。
*   `pymarl2-smac-hard/src/learners/`: 包含了各种算法的学习器实现（例如 `q_learner.py`, `coma_learner.py`, `ppo_learner.py` 等）。
*   `pymarl2-smac-hard/src/controllers/`: 包含了多智能体控制器 (MAC) 的实现，负责管理智能体的网络前向传播和动作选择。
*   `pymarl2-smac-hard/src/modules/agents/`: 包含了各种智能体网络结构（如 RNN 智能体）。
*   `pymarl2-smac-hard/src/modules/mixers/`: 包含了 QMIX、VDN 等混合网络实现。
*   `pymarl2-smac-hard/src/run/`: 包含了训练脚本，如 `run.py` (默认训练流程), `per_run.py` (优先经验回放), `dop_run.py` (DOP 算法)。

**如何参考和集成：**

1.  **理解算法**: 首先，选择一个你感兴趣的算法（例如 QMIX）。阅读其对应的 `config/algs/qmix.yaml` 文件，了解其配置参数。然后深入研究 `learners/nq_learner.py` (QMIX 对应的学习器)、`controllers/n_controller.py` (QMIX 对应的 MAC) 和 `modules/mixers/qmix.py` (QMIX 混合网络) 等文件，理解算法的原理和实现细节。

2.  **训练模型**:
    *   你可以修改 `pymarl2-smac-hard/src/main.py` 来加载不同的算法配置并进行训练。
    *   例如，要训练 QMIX 算法在 `3s5z` 地图上，你可以在 `pymarl2-smac-hard` 目录下运行类似以下命令（具体参数可能需要根据你的环境和需求调整）：
        ```bash
        python3 src/main.py --config=qmix --env-config=sc2 with env_args.map_name=3s5z
        ```
    *   训练过程中，模型会保存到 `results/models/` 目录下。

3.  **集成到 `smac-hard_v1/submission`**:
    *   **加载模型**: 在你的 `submission/teamX/red.py` 或 `blue.py` 中的 `PolicyRed`/`PolicyBlue` 类的 `__init__` 方法中，加载你训练好的模型。你需要确保模型能够正确地从保存的路径加载。
    *   **决策逻辑**: 在 `policy(self, env)` 方法中，你需要：
        *   获取当前智能体的局部观测：`obs = env.get_obs_agent(self.agent_id)` (假设你的策略是为单个智能体设计的，或者你需要遍历所有智能体获取各自观测)。
        *   将观测输入到你加载的模型中，获取动作。
        *   **重要**: 确保你的模型在推理阶段只使用局部观测，并且不依赖任何全局状态信息。
        *   返回一个包含所有智能体动作的列表。

**示例集成思路 (伪代码):**

```python
# submission/team1/red.py

import torch as th
from pymarl2_smac_hard.src.modules.agents import REGISTRY as agent_REGISTRY
from pymarl2_smac_hard.src.controllers import REGISTRY as mac_REGISTRY
# 可能还需要导入其他必要的模块，如 mixer 等

class PolicyRed:
    def __init__(self):
        # 初始化模型和加载预训练权重
        # 假设你有一个预训练的QMIX模型
        # 这里需要根据你的模型实际结构和加载方式进行调整
        # 例如，你需要定义args对象来初始化MAC和Agent
        # self.args = ... # 从配置文件或硬编码中获取必要的参数
        # self.mac = mac_REGISTRY[self.args.mac](scheme, groups, self.args)
        # self.mac.load_models("path/to/your/trained/model")
        
        # 示例：加载一个简单的RNN Agent
        # 假设你的agent_args包含了rnn_hidden_dim, n_actions等
        # self.agent_args = SimpleNamespace(rnn_hidden_dim=64, n_actions=env_info["n_actions"])
        # self.agent = agent_REGISTRY["rnn"](input_shape, self.agent_args)
        # self.agent.load_state_dict(th.load("path/to/your/agent.th"))
        # self.agent.eval() # 设置为评估模式

        # 假设你有一个简单的决策树策略
        self.iteration = 0
        self.map_name = "3s5z" # 或者从env中获取
        # 导入smac_hard/env/scripts/utils中的辅助函数
        from smac_hard.env.scripts.utils.distance_api import distance_to, nearest_n_units, center
        from smac_hard.env.scripts.utils.actions_api import attack, move
        from smac_hard.env.scripts.utils.units_api import init_unit, MAP_UNITS_TYPES, UnitTypeId

        self.distance_to = distance_to
        self.nearest_n_units = nearest_n_units
        self.center = center
        self.attack = attack
        self.move = move
        self.init_unit = init_unit
        self.MAP_UNITS_TYPES = MAP_UNITS_TYPES
        self.UnitTypeId = UnitTypeId

        self.stalkers = []
        self.zealots = []
        self.ally_list = ['stalkers', 'zealots']
        self.enemy_stalkers = []
        self.enemy_zealots = []
        self.enemy_list = ['enemy_stalkers', 'enemy_zealots']


    def policy(self, env):
        # 获取当前环境观测
        # 注意：这里只能使用局部观测，不能使用 env.get_state()
        obs_all_agents = env.get_obs() # 获取所有智能体的观测列表
        avail_actions_all_agents = env.get_avail_actions() # 获取所有智能体的可用动作列表

        actions_list = []
        
        # 示例：使用决策树策略 (参考 smac_hard/env/scripts 中的脚本)
        # 这是一个简化的示例，实际可能需要更复杂的逻辑
        
        # 模拟 init_unit
        raw_units = env._obs_list[0].observation.raw_data.units # 获取红方视角下的所有单位
        agents = [unit for unit in raw_units if unit.owner==2] # 红方单位
        enemies = [unit for unit in raw_units if unit.owner==1] # 蓝方单位

        self.stalkers = sorted([agent for agent in agents if agent.unit_type==self.MAP_UNITS_TYPES[self.map_name]['ally'][0]], key=lambda u: u.tag)
        self.zealots = sorted([agent for agent in agents if agent.unit_type==self.MAP_UNITS_TYPES[self.map_name]['ally'][1]], key=lambda u: u.tag)
        self.enemy_stalkers = sorted([enemy for enemy in enemies if enemy.unit_type==self.MAP_UNITS_TYPES[self.map_name]['enemy'][0]], key=lambda u: u.tag)
        self.enemy_zealots = sorted([enemy for enemy in enemies if enemy.unit_type==self.MAP_UNITS_TYPES[self.map_name]['enemy'][1]], key=lambda u: u.tag)

        all_ally_units = self.stalkers + self.zealots
        all_enemy_units = self.enemy_stalkers + self.enemy_zealots

        if not all_ally_units or not all_enemy_units:
            # 如果没有单位或敌人，返回停止动作
            return [1] * env.n_agents_list[0] # 假设1是停止动作的索引

        # 简单的攻击最近敌人的策略
        for unit in all_ally_units:
            if unit.health > 0:
                closest_enemy = self.nearest_n_units(unit, all_enemy_units, 1)[0]
                actions_list.append(self.attack(unit, closest_enemy))
            else:
                actions_list.append(0) # 死亡单位执行no-op

        self.iteration += 1
        return actions_list
```

## 5. 训练策略建议

*   **初期**:
    *   **随机策略**: 作为基线，可以实现一个简单的随机策略，让智能体随机选择可用动作。
    *   **Single-mode 对手策略**: `smac-hard_v1` 环境支持 `mode='single'`，此时你可以让一方智能体与内置的启发式 AI 对抗。这有助于快速验证你的智能体是否能学习到基本行为。
    *   **自博弈 (Self-Play)**: 让你的红方策略与蓝方策略（可以是同一个模型或不同训练阶段的模型）进行对抗。这是多智能体强化学习中常用的训练方法，可以帮助智能体学习到更鲁棒的策略。

*   **后期**:
    *   **���型对抗**: 当不同队伍训练出各自的模型后，可以互相使用对方的模型作为对手进行训练，以提高策略的泛化能力和对抗性。
    *   **多策略训练**: 尝试训练多个不同策略的智能体，并在评估时进行组合或选择。

**重要提示：**
*   环境仍在测试中，如果遇到环境相关的问题，请及时联系课程负责人。
*   祝你在作业中取得好成绩！
