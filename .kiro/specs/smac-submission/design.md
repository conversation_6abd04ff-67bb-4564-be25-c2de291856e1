# 设计文档

## 概述

本设计文档详细说明了如何构建一个独立的SMAC-HARD-MULTI提交包，并修复已知问题。我们将遵循一个系统化的方法，首先分析现有代码，然后进行必要的修改，最后验证修改的有效性。

## 架构

提交包的架构应该是自包含的，包括以下组件：

1. **主要脚本**：red.py和blue.py，用于实现智能体的策略。
2. **模块**：包括agents、mixers、utils和components等，用于支持主要脚本的运行。
3. **配置文件**：包括model_config.yaml，用于配置模型参数。
4. **模型文件**：包括agent.th和mixer.th，用于加载训练好的模型参数。

## 组件和接口

### 主要脚本

red.py和blue.py是提交包的入口点，它们负责加载模型参数并实现智能体的策略。它们应该能够：

1. 加载配置文件中的参数。
2. 初始化智能体和混合器。
3. 实现policy方法，根据观察选择动作。

### 模块

提交包应该包含以下模块：

1. **agents**：包含智能体的实现，如n_rnn_agent.py。
2. **mixers**：包含混合器的实现，如qmix.py。
3. **utils**：包含辅助函数，如th_utils.py。
4. **components**：包含组件，如action_selectors.py。

每个模块都应该有一个__init__.py文件，用于导出必要的类和函数。

### 配置文件

model_config.yaml应该包含所有模型初始化所需的参数，包括：

1. n_agents：智能体数量。
2. n_actions：动作空间大小。
3. rnn_hidden_dim：RNN隐藏层维度。
4. mixing_embed_dim：混合器嵌入维度。
5. hypernet_embed：超网络嵌入维度。
6. obs_shape：观察空间维度。
7. state_shape：状态空间维度。

### 模型文件

agent.th和mixer.th是训练好的模型参数文件，应该放置在models/目录下。

## 数据模型

提交包中的数据流如下：

1. 环境提供观察和状态。
2. 智能体根据观察选择动作。
3. 混合器根据所有智能体的动作和状态计算Q值。
4. 策略根据Q值选择最终的动作。

## 错误处理

我们需要处理以下错误：

1. **导入错误**：修复REGISTRY导入问题。
2. **模型输入维度不匹配**：修正agent_input_shape的计算。
3. **不可用动作**：完善动作修正逻辑。

## 测试策略

我们将使用以下步骤测试提交包：

1. 运行test.py，验证提交包能否正确加载和运行。
2. 检查日志，确保没有错误。
3. 验证智能体的行为是否符合预期。

## 实现计划

1. 分析red.py和blue.py中的导入语句。
2. 复制必要的模块到提交包中。
3. 修正导入路径为相对导入。
4. 创建model_config.yaml文件。
5. 修复REGISTRY导入问题。
6. 修正agent_input_shape的计算。
7. 完善动作修正逻辑。
8. 运行test.py验证修改。